
export interface GroupedBooking {
  id: string; // ID of the first booking in the group
  booking_date: string;
  start_time: string; // Combined start time (earliest)
  end_time: string; // Combined end time (latest)
  total_price: number; // Sum of all bookings in group
  status: 'confirmed' | 'cancelled' | 'completed' | 'pending';
  booking_reference: string | null;
  payment_reference: string | null;
  payment_status: string | null;
  payment_method?: string;
  slot_count: number; // Number of slots in this group
  court: {
    name: string;
    venue: {
      name: string;
      id: string;
    };
    sport: {
      name: string;
      id: string;
    };
  };
  individual_bookings: Array<{
    id: string;
    start_time: string;
    end_time: string;
    total_price: number;
    status: string;
    cancellation_reason?: string | null;
  }>; // Keep reference to individual bookings for admin purposes
  guest_name?: string | null;
  guest_phone?: string | null;
  user_info?: any;
  admin_booking?: any;
}

interface Booking {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  status: 'confirmed' | 'cancelled' | 'completed' | 'pending';
  booking_reference: string | null;
  payment_reference: string | null;
  payment_status: string | null;
  payment_method?: string;
  cancellation_reason?: string | null;
  court: {
    name: string;
    venue: {
      name: string;
      id: string;
    };
    sport: {
      name: string;
      id: string;
    };
  };
  guest_name?: string | null;
  guest_phone?: string | null;
  user_info?: any;
  admin_booking?: any;
}

export function groupConsecutiveBookings(bookings: Booking[]): GroupedBooking[] {
  if (!bookings || bookings.length === 0) return [];

  // ✅ DEBUG: Log original booking order from database
  console.log('🔍 Original booking order from database (should be created_at DESC):');
  bookings.slice(0, 3).forEach((booking, index) => {
    console.log(`  ${index + 1}. ${booking.booking_reference} - Created: ${booking.created_at} - Date: ${booking.booking_date}`);
  });

  // ✅ FIXED: Sort bookings by created_at (newest first), then by booking_date, then by start time
  // This preserves the database sort order and shows newest created bookings first
  const sortedBookings = [...bookings].sort((a, b) => {
    // Primary sort: created_at (newest first)
    const createdAtCompare = new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    if (createdAtCompare !== 0) return createdAtCompare;

    // Secondary sort: booking_date (newest first)
    const dateCompare = new Date(b.booking_date).getTime() - new Date(a.booking_date).getTime();
    if (dateCompare !== 0) return dateCompare;

    // Tertiary sort: start_time (earliest first)
    return a.start_time.localeCompare(b.start_time);
  });

  // ✅ DEBUG: Log final sorted order after client-side sorting
  console.log('✅ Final sorted order after client-side sorting (should maintain created_at DESC):');
  sortedBookings.slice(0, 3).forEach((booking, index) => {
    console.log(`  ${index + 1}. ${booking.booking_reference} - Created: ${booking.created_at} - Date: ${booking.booking_date}`);
  });

  const groups: GroupedBooking[] = [];
  const processedBookingIds = new Set<string>();

  sortedBookings.forEach((booking) => {
    if (processedBookingIds.has(booking.id)) return;

    // Start a new group with this booking
    const group: GroupedBooking = {
      id: booking.id,
      booking_date: booking.booking_date,
      start_time: booking.start_time,
      end_time: booking.end_time,
      total_price: booking.total_price,
      status: booking.status,
      booking_reference: booking.booking_reference,
      payment_reference: booking.payment_reference,
      payment_status: booking.payment_status,
      payment_method: booking.payment_method,
      slot_count: 1,
      court: booking.court,
      individual_bookings: [{
        id: booking.id,
        start_time: booking.start_time,
        end_time: booking.end_time,
        total_price: booking.total_price,
        status: booking.status,
        cancellation_reason: booking.cancellation_reason
      }],
      guest_name: booking.guest_name,
      guest_phone: booking.guest_phone,
      user_info: booking.user_info,
      admin_booking: booking.admin_booking
    };

    processedBookingIds.add(booking.id);

    // Look for consecutive bookings that can be grouped
    if (booking.payment_reference) {
      const consecutiveBookings = findConsecutiveBookings(
        booking,
        sortedBookings,
        processedBookingIds
      );

      consecutiveBookings.forEach((consecutiveBooking) => {
        group.end_time = consecutiveBooking.end_time;
        group.total_price += consecutiveBooking.total_price;
        group.slot_count += 1;
        group.individual_bookings.push({
          id: consecutiveBooking.id,
          start_time: consecutiveBooking.start_time,
          end_time: consecutiveBooking.end_time,
          total_price: consecutiveBooking.total_price,
          status: consecutiveBooking.status,
          cancellation_reason: consecutiveBooking.cancellation_reason
        });
        processedBookingIds.add(consecutiveBooking.id);
      });
    }

    groups.push(group);
  });

  return groups;
}

function findConsecutiveBookings(
  baseBooking: Booking,
  allBookings: Booking[],
  processedIds: Set<string>
): Booking[] {
  const consecutive: Booking[] = [];
  let currentEndTime = baseBooking.end_time;

  // Look for bookings that start when the current one ends
  while (true) {
    const nextBooking = allBookings.find(b => 
      !processedIds.has(b.id) &&
      b.payment_reference === baseBooking.payment_reference &&
      b.booking_date === baseBooking.booking_date &&
      b.court.venue.id === baseBooking.court.venue.id &&
      b.court.name === baseBooking.court.name &&
      b.start_time === currentEndTime &&
      b.status === baseBooking.status // Only group bookings with same status
    );

    if (!nextBooking) break;

    consecutive.push(nextBooking);
    currentEndTime = nextBooking.end_time;
  }

  return consecutive;
}

export function getGroupedBookingCount(bookings: Booking[]): number {
  return groupConsecutiveBookings(bookings).length;
}
