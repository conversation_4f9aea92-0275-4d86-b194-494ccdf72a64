import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import BookingManagement from './BookingManagement';
import VenueManagement from './VenueManagement';
import SupportButton from '@/components/SupportButton';

import CourtManagement from './CourtManagement';
import TemplateSlotManagement from './TemplateSlotManagement';
import AnalyticsDashboard from './AnalyticsDashboard';
import AnalyticsDesktop from './AnalyticsDesktop';
import ReviewManagement from './ReviewManagement';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast as hotToast } from "react-hot-toast";
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
// Excel formatting utilities imported for future enhancement
import { getDisplayEmail } from '@/utils/security';
import {
  getOptimizedDashboardStats,
  getOptimizedPopularCourts,
  getOptimizedCustomRevenue,
  getOptimizedVenuesWithStats,
  logPerformanceImprovement
} from '@/utils/dashboardOptimization';


import { NotificationAnalytics } from '@/components/admin/NotificationAnalytics';
import { NotificationTemplateLibrary } from '@/components/admin/NotificationTemplateLibrary';
import {
  Activity,
  TrendingUp,
  Banknote,
  Award,
  Eye,
  ChevronRight,
  Clock,
  Users,
  Loader2,
  Download,
  Calendar,
  Map,
  Dumbbell,
  BarChart,
  MessageCircle,
  Star,
  HelpCircle,
  DollarSign,
  LogOut,
  UserCircle,
  Ban,
  Crown
} from 'lucide-react';

import HelpRequestsManagement from './HelpRequestsManagement';

import EarningsDashboard from './EarningsDashboard';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { getDisplayName } from '@/utils/security';

interface VenueAdmin {
  venue_id: string;
}

// Interfaces for dashboard data
interface QuickStats {
  todayBookings: number;
  averageRating: number;
  occupancyRate: number;
  isLoading: boolean;
  pendingBookings: number;
  monthlyRevenue: number;
  upcomingBookings: number;
  recentReviews: number;
}

interface WeatherData {
  current: {
    temp: number;
  };
  severe?: any[];
  forecast: Array<{
    time: string;
    temp: number;
    precipitation?: number;
  }>;
  daily?: Array<{
    date: string;
    day: string;
    temp_max: number;
    temp_min: number;
  }>;
}

interface OptimizedDashboardStats {
  todayBookings: number;
  pendingBookings: number;
  upcomingBookings: number;
  averageRating: number;
  recentReviews: number;
  todaysRevenue: number;
  occupancyRate: number;
  userRole: string;
  venueCount: number;
  venueIds: string[];
}

interface Venue {
  id: string;
  name: string;
  bookings_count?: number;
  total_revenue?: number;
}

interface UserProfile {
  id: string;
  full_name?: string;
  email?: string;
  phone?: string;
}

interface BookingData {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  payment_method?: string;
  status: string;
  created_at: string;
  guest_name?: string;
  court?: {
    name: string;
    venue?: {
      id: string;
      name: string;
    };
  };
  user?: {
    id?: string;
    full_name?: string;
    email?: string;
    phone?: string;
  };
}

interface RevenueReportRow {
  'Booking Date': string;
  'Time Slot': string;
  'Venue': string;
  'Court': string;
  'Customer Name': string;
  'Customer Email': string;
  'Customer Phone': string;
  'Coupon Applied': string;
  'Coupon Code': string;
  'Original Amount (₹)': number;
  'Discount Amount (₹)': number;
  'Final Amount (₹)': number;
  'Gross Amount (₹)': number;
  'Platform Fee (₹)': string;
  'TDS Amount (₹)': string;
  'Net Amount (₹)': string;
  'Payment Method': string;
  'Payment Status': string;
  'Created At': string;
  'Platform Fee %': number;
  'TDS Rate %': number;
}

// For debug: allow window.__filteredBookings
declare global {
  interface Window {
    __filteredBookings?: any;
  }
}

// WeatherWidget for AdminHome
const WeatherWidget: React.FC<{ venueId: string }> = ({ venueId }) => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const fetchWeather = async () => {
      setLoading(true);
      setError(null);
      try {
        const sessionResult = await supabase.auth.getSession();
        const jwt = sessionResult.data.session?.access_token;
        if (!jwt) throw new Error('Not authenticated');
        const weatherUrl = 'https://lrtirloetmulgmdxnusl.supabase.co/functions/v1/weather-proxy';
        const res = await fetch(weatherUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwt}`,
          },
          body: JSON.stringify({ venue_id: venueId, daily: true }),
        });
        const weatherData = await res.json();
        if (!res.ok) throw new Error(weatherData.error || 'Failed to fetch weather');
        setWeather(weatherData);
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };
    if (venueId) fetchWeather();
  }, [venueId]);

  if (!venueId) return null;

  // Helper: Weather Impact Score
  function getImpactScore() {
    if (!weather) return null;
    if (weather.severe && weather.severe.length > 0) return { label: 'Poor', color: 'bg-red-500' };
    if (weather.forecast && weather.forecast.some((f: any) => f.precipitation > 2)) return { label: 'Moderate', color: 'bg-yellow-500' };
    return { label: 'Good', color: 'bg-green-500' };
  }
  const impact = getImpactScore();

  return (
    <Card className="mb-4 cursor-pointer" onClick={() => setExpanded(e => !e)}>
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-base">Weather Forecast</CardTitle>
        {impact && (
          <span className={`text-xs px-2 py-1 rounded ${impact.color} text-white`}>{impact.label} for outdoor play</span>
        )}
      </CardHeader>
      <CardContent className="pt-0">
        {loading && <div className="text-xs text-gray-500">Loading weather...</div>}
        {error && <div className="text-xs text-red-500">{error}</div>}
        {weather && (
          <>
            {/* Current Weather */}
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">{Math.round(weather.current.temp)}°C</span>
              <span className="text-xs text-gray-500">Now</span>
              {weather.severe && weather.severe.length > 0 && (
                <Badge variant="destructive">Severe Weather</Badge>
              )}
            </div>
            {/* 3-Day Compact Forecast */}
            {weather.daily && (
              <div className="flex gap-2 mb-2">
                {weather.daily.slice(0, 3).map((d: any, i: number) => (
                  <div key={d.date} className="flex flex-col items-center min-w-[56px]">
                    <span className="text-xs text-gray-500 font-medium">{d.day}</span>
                    <span className="text-lg font-bold">{Math.round(d.temp_max)}°</span>
                    <span className="text-xs text-gray-400">{Math.round(d.temp_min)}°</span>
                    <span className="text-xs">{d.icon}</span>
                    {d.rain > 0 && <span className="text-blue-500 text-xs">{d.rain}mm</span>}
                  </div>
                ))}
              </div>
            )}
            {/* Hourly Rain/Storm Timeline (next 12h) */}
            <div className="flex gap-1 overflow-x-auto pb-1">
              {weather.forecast.slice(0, 12).map((f: any, i: number) => (
                <div key={f.time} className="flex flex-col items-center min-w-[40px]">
                  <span className="text-xs text-gray-500">{new Date(f.time).getHours()}:00</span>
                  <span className="font-medium text-sm">{Math.round(f.temp)}°</span>
                  {f.precipitation > 0 && (
                    <span className="text-blue-500 text-xs">{f.precipitation}mm</span>
                  )}
                  {[95,96,99].includes(f.weathercode) && (
                    <span className="text-red-500 text-xs">Storm</span>
                  )}
                </div>
              ))}
            </div>
            {/* Expandable details */}
            {expanded && weather.daily && (
              <div className="mt-2">
                <div className="text-xs text-gray-400 mb-1">3-Day Details</div>
                <div className="flex gap-2">
                  {weather.daily.slice(0, 3).map((d: any, i: number) => (
                    <div key={d.date} className="flex flex-col items-center min-w-[80px] p-2 rounded bg-green-600/80 text-white">
                      <span className="text-xs font-medium">{d.day}</span>
                      <span className="text-lg font-bold">{Math.round(d.temp_max)}° / {Math.round(d.temp_min)}°</span>
                      <span className="text-xs">{d.icon}</span>
                      <span className="text-xs text-blue-200">Rain: {d.rain}mm</span>
                      <span className="text-xs text-white/80">{d.summary}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

const AdminHome: React.FC = () => {
  const {
    user,
    userRole,
    signOut
  } = useAuth();
  const [adminVenues, setAdminVenues] = useState<VenueAdmin[]>([]);
  const [activeTab, setActiveTab] = useState('dashboard');

  // Dashboard state management (matching mobile)
  const [stats, setStats] = useState<QuickStats>({
    todayBookings: 0,
    averageRating: 0,
    occupancyRate: 0,
    isLoading: true,
    pendingBookings: 0,
    monthlyRevenue: 0,
    upcomingBookings: 0,
    recentReviews: 0
  });

  const [popularCourts, setPopularCourts] = useState<Array<{ court_name: string; bookings_percentage: number }>>([]);
  const [courtDataLoading, setCourtDataLoading] = useState(true);
  const [todaysRevenue, setTodaysRevenue] = useState(0);
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [customRevenue, setCustomRevenue] = useState<number>(0);
  // PHASE 2: Online vs Offline Revenue Tracking
  const [onlineRevenue, setOnlineRevenue] = useState<number>(0);
  const [offlineRevenue, setOfflineRevenue] = useState<number>(0);
  const [onlineBookings, setOnlineBookings] = useState<number>(0);
  const [offlineBookings, setOfflineBookings] = useState<number>(0);
  // FIX: Add Net Settlement tracking for correct display
  const [onlineNetRevenue, setOnlineNetRevenue] = useState<number>(0);
  // TDS: Add TDS tracking for display
  const [onlineTdsAmount, setOnlineTdsAmount] = useState<number>(0);
  const [totalTdsAmount, setTotalTdsAmount] = useState<number>(0);
  const [venuesWithStats, setVenuesWithStats] = useState<Array<{ id: string; name: string; platform_fee_percentage: number }>>([]);
  const [venueSubscribers, setVenueSubscribers] = useState<Record<string, number>>({});
  // NEW: Add venue selector state for Custom Revenue Report
  const [selectedRevenueVenueId, setSelectedRevenueVenueId] = useState<string>('all');
  const [pendingVenueSelection, setPendingVenueSelection] = useState<string>('all');
  const [showVenueConfirmation, setShowVenueConfirmation] = useState<boolean>(false);

  const [broadcastModal, setBroadcastModal] = useState<{ open: boolean; venueId: string | null }>({ open: false, venueId: null });
  const [broadcastTitle, setBroadcastTitle] = useState('');
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const [broadcastLoading, setBroadcastLoading] = useState(false);
  const approvedBroadcastIdsRef = useRef<Set<string>>(new Set());
  const [broadcastHistory, setBroadcastHistory] = useState<Record<string, any[]>>({});
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [avgCustomFee, setAvgCustomFee] = useState(5);

  useEffect(() => {
    if (user) {
      fetchAdminVenues();
      fetchDashboardData();
    }
  }, [user]);

  // Fetch subscriber counts for venues (matching mobile implementation)
  const fetchSubscribers = async () => {
    if (!adminVenues.length) return;

    console.log('🔄 Fetching subscriber counts for venues...');
    const counts: Record<string, number> = {};

    try {
      for (const venue of adminVenues) {
        const { count, error } = await supabase
          .from('venue_subscriptions')
          .select('id', { count: 'exact' })
          .eq('venue_id', venue.venue_id);

        if (error) {
          console.error(`Error fetching subscribers for venue ${venue.venue_id}:`, error);
          counts[venue.venue_id] = 0;
        } else {
          counts[venue.venue_id] = count || 0;
        }
      }

      console.log('✅ Subscriber counts fetched:', counts);
      setVenueSubscribers(counts);
    } catch (error) {
      console.error('Error in fetchSubscribers:', error);
    }
  };

  // Fetch subscribers when adminVenues changes
  useEffect(() => {
    if (adminVenues.length > 0) {
      fetchSubscribers();
      fetchBroadcastHistory();
    }
  }, [adminVenues]);
  const fetchAdminVenues = async () => {
    try {


      if (userRole === 'super_admin') {
        // For super admins, get all venues
        const {
          data,
          error
        } = await supabase.from('venues').select('id').eq('is_active', true);
        if (error) throw error;
        // Transform the data to match the VenueAdmin interface
        const transformedData = data?.map(item => ({
          venue_id: item.id
        })) || [];
        setAdminVenues(transformedData);
      } else {
        // For regular admins, get only assigned venues
        const {
          data,
          error
        } = await supabase.rpc('get_admin_venues');
        if (error) throw error;
        setAdminVenues(data || []);
      }
    } catch (error) {
      console.error('Error fetching admin venues:', error);
    }
  };

  // Dashboard data fetching (matching mobile implementation)
  const fetchDashboardData = async () => {
    if (!user?.id) return;

    try {
      console.log('🚀 Starting optimized dashboard fetch...');
      const overallStartTime = performance.now();

      // Set loading state
      setStats(prev => ({ ...prev, isLoading: true }));
      setCourtDataLoading(true);

      // ✅ PERFORMANCE OPTIMIZATION: Single query replaces 8+ separate queries
      const dashboardStats = await getOptimizedDashboardStats(user.id);

      // ✅ PERFORMANCE OPTIMIZATION: Single query for popular courts
      const popularCourtsData = await getOptimizedPopularCourts(user.id);

      // Update state with optimized data
      setStats({
        todayBookings: dashboardStats.todayBookings || 0,
        averageRating: dashboardStats.averageRating || 0,
        occupancyRate: dashboardStats.occupancyRate || 0,
        isLoading: false,
        pendingBookings: dashboardStats.pendingBookings || 0,
        monthlyRevenue: dashboardStats.todaysRevenue || 0,
        upcomingBookings: dashboardStats.upcomingBookings || 0,
        recentReviews: dashboardStats.recentReviews || 0
      });

      setTodaysRevenue(dashboardStats.todaysRevenue || 0);
      setPopularCourts(popularCourtsData || []);
      setCourtDataLoading(false);

      // Fetch venues with stats
      const venuesData = await getOptimizedVenuesWithStats(user.id, userRole || 'user', adminVenues);
      setVenuesWithStats(venuesData || []);

      const overallEndTime = performance.now();
      const totalTime = overallEndTime - overallStartTime;
      // Dashboard loaded successfully

      // Log performance improvement
      logPerformanceImprovement('Dashboard Load', 4000, totalTime, user.id);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setStats(prev => ({ ...prev, isLoading: false }));
    }
  };

  const fetchBroadcastHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('type', 'venue_broadcast')
        .order('created_at', { ascending: false });
      if (!error && data) {
        // Group by venue_id
        const grouped: Record<string, any[]> = {};
        data.forEach((n: any) => {
          const venueId = n.metadata?.venue_id;
          if (!venueId) return;
          if (!grouped[venueId]) grouped[venueId] = [];
          grouped[venueId].push(n);
        });
        setBroadcastHistory(grouped);
      }
    } catch (error) {
      console.error('Error fetching broadcast history:', error);
    }
  };

  // Helper to get the correct default fee for this admin
  function getDefaultVenueFee() {
    // For super_admin, use first venue; for regular admin, use their assigned venue
    if (userRole === 'super_admin') {
      return venuesWithStats[0]?.platform_fee_percentage ?? 5;
    }
    // Find the first venue in adminVenues that matches venuesWithStats
    const adminVenueId = adminVenues[0]?.venue_id;
    if (!adminVenueId) return 5;
    const venue = venuesWithStats.find(v => v.id === adminVenueId);
    return venue?.platform_fee_percentage ?? 5;
  }

  // Custom revenue calculation - FIXED: Use same RPC function as Settlement Report
  const calculateCustomRevenue = async () => {
    if (!customStartDate || !customEndDate || !user?.id) {
      setCustomRevenue(0);
      setAvgCustomFee(getDefaultVenueFee());
      return;
    }

    try {
      const startDate = typeof customStartDate === 'string' ? new Date(customStartDate) : customStartDate;
      const endDate = typeof customEndDate === 'string' ? new Date(customEndDate) : customEndDate;
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      // PHASE 2: Use enhanced RPC function with online/offline separation
      const { data: revenueData, error } = await supabase
        .rpc('get_custom_revenue_with_option_b', {
          admin_user_id: user.id,
          start_date: startDateStr,
          end_date: endDateStr,
          filter_venue_id: selectedRevenueVenueId === 'all' ? null : selectedRevenueVenueId
        });

      if (error) throw error;

      console.log('🔍 Phase 2 Revenue Data:', revenueData);

      // Set revenue values with online/offline separation and TDS
      setCustomRevenue(revenueData?.grossRevenue || 0);
      setOnlineRevenue(revenueData?.onlineGrossRevenue || 0);
      setOfflineRevenue(revenueData?.offlineGrossRevenue || 0);
      setOnlineBookings(revenueData?.onlineBookings || 0);
      setOfflineBookings(revenueData?.offlineBookings || 0);
      setOnlineTdsAmount(revenueData?.onlineTdsAmount || 0);
      setTotalTdsAmount(revenueData?.tdsAmount || 0);

      // FIX: Calculate and set Net Settlement from the booking data
      // We need to calculate this from actual booking data, not from the RPC summary

      // PHASE 3: Fetch booking data including cancelled bookings for Excel export
      const { data: rpcData, error: bookingError } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: startDateStr,
          end_date: endDateStr
        });

      if (bookingError) throw bookingError;

      // Transform RPC data to match expected structure
      const bookingsData = rpcData?.map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
        profile_data: booking.profile_data, // FIXED: Preserve profile data for customer info
        cancellation_data: booking.cancellation_data // PHASE 3: Include cancellation data
      })) || [];

      // Filter by admin venues and selected venue
      const filteredBookings = bookingsData.filter(booking => {
        // FIXED: Use the correct venue ID from the RPC data structure
        const bookingVenueId = booking.court_data?.venue?.id || booking.court?.venue?.id;

        // First check admin venue access
        const hasVenueAccess = userRole === 'super_admin' ||
          adminVenues.some(v => v.venue_id === bookingVenueId);

        if (!hasVenueAccess) return false;

        // Then check venue selection filter
        if (selectedRevenueVenueId === 'all') return true;
        const matches = bookingVenueId === selectedRevenueVenueId;

        // Debug logging for venue filtering
        console.log('🔍 Custom Revenue Venue Filter:', {
          bookingId: booking.id,
          venueId: bookingVenueId,
          venueName: booking.court_data?.venue?.name || booking.court?.venue?.name,
          selectedVenueId: selectedRevenueVenueId,
          matches,
          dataStructure: {
            court_data: !!booking.court_data,
            court: !!booking.court
          }
        });

        return matches;
      });

      // Expose for debug UI
      if (typeof window !== 'undefined') {
        window.__filteredBookings = filteredBookings;
      }

      // Calculate total revenue using original amounts (before discounts) for consistency with Settlement Report
      const totalRevenue = filteredBookings.reduce((sum, booking) => {
        const couponUsageArray = booking.coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
        return sum + originalAmount;
      }, 0);
      setCustomRevenue(totalRevenue);

      // FIX: Calculate Net Settlement for online bookings (confirmed/completed only)
      const confirmedBookings = filteredBookings.filter(b => b.status === 'confirmed' || b.status === 'completed');
      const onlineBookings = confirmedBookings.filter(b => b.payment_method === 'online');

      const calculatedNetSettlement = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = b.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);

      setOnlineNetRevenue(calculatedNetSettlement);

      // Calculate average fee percent for display
      const avgFee = filteredBookings.length > 0
        ? (filteredBookings.reduce((sum, b) => sum + (b.court?.venue?.platform_fee_percentage ?? 5), 0) / filteredBookings.length)
        : getDefaultVenueFee();
      setAvgCustomFee(avgFee);
    } catch (error) {
      console.error('Error calculating custom revenue:', error);
      setCustomRevenue(0);
      setAvgCustomFee(getDefaultVenueFee());
    }
  };

  // Add useEffect to trigger revenue calculation when dates or venue selection change
  useEffect(() => {
    calculateCustomRevenue();
  }, [customStartDate, customEndDate, user?.id, selectedRevenueVenueId, userRole, adminVenues]);

  // Update useEffect to handle cases where only one date is selected
  useEffect(() => {
    if ((!customStartDate || !customEndDate) && venuesWithStats.length > 0) {
      setAvgCustomFee(getDefaultVenueFee());
    }
  }, [venuesWithStats, customStartDate, customEndDate, userRole, adminVenues]);

  // Download revenue report function
  const downloadRevenueReport = async () => {
    if (!customStartDate || !customEndDate || !user?.id) {
      hotToast.error('Please select both start and end dates');
      return;
    }

    setDownloadingReport(true);

    try {
      // Format dates for the query
      const startDate = typeof customStartDate === 'string' ? new Date(customStartDate) : customStartDate;
      const endDate = typeof customEndDate === 'string' ? new Date(customEndDate) : customEndDate;
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      // ISSUE 1 FIX: Get bookings INCLUDING cancelled bookings for Excel export (same as mobile)
      console.log('🔄 Using RPC function to fetch booking data with coupons including cancelled...');
      const { data: rpcData, error } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: startDateStr,
          end_date: endDateStr
        });

      if (error) throw error;

      // Transform RPC data to match expected structure
      const bookingsData = rpcData?.map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
        profile_data: booking.profile_data // FIXED: Preserve profile data for customer info
      })) || [];

      console.log('🔍 Raw query result:', {
        totalBookings: bookingsData?.length,
        sampleBooking: bookingsData?.[0],
        userRole,
        adminVenues: adminVenues.length
      });

      // Process booking data for revenue calculations

      // Filter by admin venues AND selected venue filter
      const filteredBookings = (bookingsData || []).filter(booking => {
        // First check admin venue access
        const hasVenueAccess = userRole === 'super_admin' ||
          adminVenues.some(v => v.venue_id === booking.court?.venue?.id);

        if (!hasVenueAccess) return false;

        // Then check venue selection filter (CRITICAL FIX: Apply selectedRevenueVenueId filter)
        if (selectedRevenueVenueId === 'all') return true;

        // FIXED: Use the correct venue ID from the RPC data structure
        const bookingVenueId = booking.court_data?.venue?.id || booking.court?.venue?.id;
        const matches = bookingVenueId === selectedRevenueVenueId;

        // Debug logging for download venue filtering
        console.log('📥 Download Venue Filter:', {
          bookingId: booking.id,
          venueId: bookingVenueId,
          venueName: booking.court_data?.venue?.name || booking.court?.venue?.name,
          selectedVenueId: selectedRevenueVenueId,
          matches,
          dataStructure: {
            court_data: !!booking.court_data,
            court: !!booking.court
          }
        });

        return matches;
      });

      // Debug: Log the raw booking data before processing
      console.log('🔍 Total bookings from query:', bookingsData?.length);
      console.log('🔍 Filtered bookings:', filteredBookings.length);
      console.log('🔍 Sample booking data:', filteredBookings[0]);

      // PHASE 3: Prepare Excel data including cancelled bookings
      const excelData: RevenueReportRow[] = filteredBookings.map(booking => {
        // Handle coupon_usage which can be null or an array
        const couponUsageArray = (booking as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const hasCoupon = !!couponUsage;

        // PHASE 3: Check if booking is cancelled
        const isCancelled = booking.status === 'cancelled';
        const cancellationData = (booking as any).cancellation_data;

        console.log('🔍 Processing booking:', booking.id, 'status:', booking.status, 'isCancelled:', isCancelled);

        // PHASE 1: OPTION B CALCULATION - Platform fee on original amount, venue bears coupon cost
        const totalPrice = Number(booking.total_price) || 0; // What user actually paid
        const originalAmount = hasCoupon ? (Number(couponUsage.original_price) || 0) : totalPrice;
        const discountAmount = hasCoupon ? (Number(couponUsage.discount_applied) || 0) : 0;
        const finalAmount = hasCoupon ? (Number(couponUsage.final_price) || 0) : totalPrice;

        const feePercent = booking.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = booking.court?.venue?.tds_rate ?? 1.0;
        // CRITICAL FIX: Platform fee calculated on ORIGINAL amount (Option B)
        const platformFee = originalAmount * (feePercent / 100);
        // TDS calculated on platform fee (online bookings only)
        const tdsAmount = booking.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        // Net settlement: What venue actually receives = User payment - Platform fee - TDS
        const netSettlement = totalPrice - platformFee - tdsAmount;

        return {
          'Booking Date': booking.booking_date,
          'Time Slot': `${booking.start_time} - ${booking.end_time}`,
          'Venue': booking.court?.venue?.name || 'N/A',
          'Court': booking.court?.name || 'N/A',
          'Customer Name': booking.profile_data?.full_name || booking.guest_name || 'N/A',
          'Customer Email': booking.profile_data?.email ? getDisplayEmail(booking.profile_data.email) : 'N/A',
          'Customer Phone': booking.profile_data?.phone || 'N/A',
          'Coupon Applied': hasCoupon ? 'YES' : 'NO',
          'Coupon Code': hasCoupon ? (couponUsage.coupon?.code || 'N/A') : 'N/A',
          // PHASE 3: Mark cancelled booking amounts as "CANCELLED"
          'Original Amount (₹)': isCancelled ? 'CANCELLED' : originalAmount,
          'Discount Amount (₹)': isCancelled ? 'CANCELLED' : discountAmount,
          'Final Amount (₹)': isCancelled ? 'CANCELLED' : finalAmount,
          'Gross Amount (₹)': isCancelled ? 'CANCELLED' : totalPrice.toFixed(2),
          'Platform Fee (₹)': isCancelled ? 'CANCELLED' : platformFee.toFixed(2),
          'TDS Amount (₹)': isCancelled ? 'CANCELLED' : tdsAmount.toFixed(2),
          'Net Amount (₹)': isCancelled ? 'CANCELLED' : netSettlement.toFixed(2),
          'Payment Method': booking.payment_method || 'N/A',
          'Payment Status': booking.status,
          'Booking Reference': booking.booking_reference || 'N/A',
          'Created At': new Date(booking.created_at).toLocaleString(),
          'Platform Fee %': feePercent
        };
      });

      // PHASE 3: Separate confirmed/completed bookings from cancelled bookings
      const confirmedBookings = filteredBookings.filter(b => b.status === 'confirmed' || b.status === 'completed');
      const cancelledBookings = filteredBookings.filter(b => b.status === 'cancelled');

      // PHASE 1: OPTION B SUMMARY CALCULATIONS (Only for confirmed/completed bookings)
      // Gross revenue = sum of original amounts (before discounts) - EXCLUDES CANCELLED
      const totalGrossRevenue = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      // Platform fee calculated on original amounts (Option B) - EXCLUDES CANCELLED
      const totalPlatformFee = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        return sum + (originalAmount * (feePercent / 100));
      }, 0);

      // TDS calculated on platform fees (online bookings only) - EXCLUDES CANCELLED
      const totalTdsAmount = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = b.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        return sum + tdsAmount;
      }, 0);

      // Net settlement = what user paid - platform fee (on original) - TDS - EXCLUDES CANCELLED
      const totalNetSettlement = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = b.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);
      const avgFee = confirmedBookings.length > 0 ? (confirmedBookings.reduce((sum, b) => sum + (b.court?.venue?.platform_fee_percentage ?? 5), 0) / confirmedBookings.length) : 5;

      // Calculate coupon summary statistics - EXCLUDES CANCELLED
      const totalCouponBookings = confirmedBookings.filter(b => {
        const couponUsageArray = (b as any).coupon_usage;
        return couponUsageArray && Array.isArray(couponUsageArray) && couponUsageArray.length > 0;
      }).length;

      const totalDiscountGiven = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        return sum + (couponUsage ? (Number(couponUsage.discount_applied) || 0) : 0);
      }, 0);

      // HUDLE APPROACH: Separate Online vs Offline Bookings for Excel Report (EXCLUDES CANCELLED)
      const onlineBookings = confirmedBookings.filter(b => b.payment_method === 'online');
      const offlineBookings = confirmedBookings.filter(b => b.payment_method === 'cash');

      // Online Bookings Calculations (Settlement-affecting) - EXCLUDES CANCELLED
      const onlineGrossRevenue = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      const onlinePlatformFee = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        return sum + (originalAmount * (feePercent / 100));
      }, 0);

      const onlineTdsAmount = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = platformFee * (tdsRate / 100);
        return sum + tdsAmount;
      }, 0);

      const onlineNetSettlement = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = platformFee * (tdsRate / 100);
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);

      // FIX: Set the correct Net Settlement value for display
      setOnlineNetRevenue(onlineNetSettlement);

      // Offline Bookings Calculations (Informational only) - EXCLUDES CANCELLED
      const offlineGrossRevenue = offlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      const offlineNetAmount = offlineBookings.reduce((sum, b) => {
        const userPayment = Number(b.total_price) || 0;
        return sum + userPayment;
      }, 0);

      // Helper function to create empty row template
      const createEmptyRow = (overrides: Partial<RevenueReportRow> = {}): RevenueReportRow => ({
        'Booking Date': '',
        'Time Slot': '',
        'Venue': '',
        'Court': '',
        'Customer Name': '',
        'Customer Email': '',
        'Customer Phone': '',
        'Coupon Applied': '',
        'Coupon Code': '',
        'Original Amount (₹)': 0,
        'Discount Amount (₹)': 0,
        'Final Amount (₹)': 0,
        'Gross Amount (₹)': 0,
        'Platform Fee (₹)': '',
        'TDS Amount (₹)': '',
        'Net Amount (₹)': '',
        'Payment Method': '',
        'Payment Status': '',
        'Created At': '',
        'Platform Fee %': 0,
        'TDS Rate %': 0,
        ...overrides
      });

      // Use mobile-style simple objects with better spacing for mobile view
      const summaryRows = [
        {},
        {},
        { 'Booking Date': '=== REVENUE SUMMARY ===', 'Time Slot': '', 'Venue': '', 'Court': '' },
        {},
        { 'Booking Date': 'Total Bookings', 'Time Slot': filteredBookings.length },
        // PHASE 3: Add cancelled booking count
        { 'Booking Date': 'Cancelled Booking Count', 'Time Slot': cancelledBookings.length },
        { 'Booking Date': 'Confirmed/Completed Bookings', 'Time Slot': confirmedBookings.length },
        { 'Booking Date': 'Bookings with Coupons', 'Time Slot': totalCouponBookings },
        {},
        { 'Booking Date': 'Total Discount Given', 'Time Slot': `₹${totalDiscountGiven.toFixed(2)}` },
        { 'Booking Date': 'Gross Revenue', 'Time Slot': `₹${totalGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Platform Fee', 'Time Slot': `₹${totalPlatformFee.toFixed(2)}` },
        { 'Booking Date': 'TDS Amount', 'Time Slot': `₹${totalTdsAmount.toFixed(2)}` },
        { 'Booking Date': 'Net Settlement (What Venue Receives)', 'Time Slot': `₹${totalNetSettlement.toFixed(2)}` },
        {},
        {},
        // HUDLE APPROACH: Online vs Offline Breakdown
        { 'Booking Date': '=== ONLINE BOOKINGS (Settlement-Affecting) ===', 'Time Slot': '', 'Venue': '', 'Court': '' },
        {},
        { 'Booking Date': 'Online Bookings Count', 'Time Slot': onlineBookings.length },
        { 'Booking Date': 'Online Gross Revenue', 'Time Slot': `₹${onlineGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Online Platform Fee', 'Time Slot': `₹${onlinePlatformFee.toFixed(2)}` },
        { 'Booking Date': 'Online TDS Amount', 'Time Slot': `₹${onlineTdsAmount.toFixed(2)}` },
        { 'Booking Date': 'Online Net Settlement', 'Time Slot': `₹${onlineNetSettlement.toFixed(2)}` },
        {},
        {},
        { 'Booking Date': '=== OFFLINE BOOKINGS (Informational Only) ===', 'Time Slot': '', 'Venue': '', 'Court': '' },
        {},
        { 'Booking Date': 'Offline Bookings Count (Cash)', 'Time Slot': offlineBookings.length },
        { 'Booking Date': 'Offline Gross Revenue (Cash)', 'Time Slot': `₹${offlineGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Offline Net Amount (Cash)', 'Time Slot': `₹${offlineNetAmount.toFixed(2)}` },
        {},
        { 'Booking Date': 'Note', 'Time Slot': 'Offline bookings are venue-managed and NOT part of Grid२Play settlements' },
        {},
        {},
        { 'Booking Date': 'Avg Platform Fee %', 'Time Slot': `${avgFee.toFixed(2)}%` },
        { 'Booking Date': 'Date Range', 'Time Slot': `${format(startDate, 'dd/MM/yyyy')} to ${format(endDate, 'dd/MM/yyyy')}` }
      ];

      // Financial summary calculated for enhanced reporting

      // Combine data with summary rows
      const allData = [...excelData, ...summaryRows];

      // Create workbook and download with enhanced formatting
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(allData);

      // Apply Grid२Play mobile-optimized formatting
      if (!ws['!rows']) ws['!rows'] = [];
      if (!ws['!cols']) ws['!cols'] = [];

      // Set mobile-optimized row heights (25 units ≈ 48px touch targets)
      for (let i = 0; i < allData.length; i++) {
        if (!ws['!rows'][i]) ws['!rows'][i] = {};
        ws['!rows'][i].hpt = 25; // Mobile-friendly row height
      }

      // Set mobile-optimized column widths
      const columnCount = Object.keys(allData[0] || {}).length;
      for (let col = 0; col < columnCount; col++) {
        if (!ws['!cols'][col]) ws['!cols'][col] = {};
        ws['!cols'][col].wch = 15; // Mobile-friendly column width
      }

      // Freeze header row for better mobile scrolling
      ws['!freeze'] = { xSplit: 0, ySplit: 1 };

      XLSX.utils.book_append_sheet(wb, ws, 'Revenue Report');

      // Generate filename with date range
      const filename = `revenue_report_${startDateStr}_to_${endDateStr}.xlsx`;
      XLSX.writeFile(wb, filename);

      hotToast.success('Revenue report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading revenue report:', error);
      hotToast.error('Failed to download report');
    } finally {
      setDownloadingReport(false);
    }
  };



  // Broadcast handling functions
  const handleSendBroadcast = async () => {
    if (!broadcastTitle || !broadcastMessage || !broadcastModal.venueId) return;

    setBroadcastLoading(true);
    try {
      // Check if venue has subscribers
      const { data: subs, error: subsError } = await supabase
        .from('venue_subscriptions')
        .select('user_id')
        .eq('venue_id', broadcastModal.venueId);

      if (subsError) throw subsError;
      if (!subs || subs.length === 0) {
        hotToast.error('No subscribers found for this venue');
        return;
      }

      // Create notification template using the new function
      const { data: templateId, error: templateError } = await supabase.rpc('create_venue_notification' as any, {
        p_venue_id: broadcastModal.venueId,
        p_title: broadcastTitle,
        p_message: broadcastMessage,
        p_created_by: user?.id,
        p_metadata: { venue_id: broadcastModal.venueId }
      });

      if (templateError) throw templateError;

      hotToast.success(`Notification sent for approval! Will be delivered to ${subs.length} subscribers once approved.`);
      setBroadcastTitle('');
      setBroadcastMessage('');
      setBroadcastModal({ open: false, venueId: null });

      // Refresh broadcast history
      await fetchBroadcastHistory();
    } catch (error) {
      console.error('Error sending broadcast:', error);
      hotToast.error('Failed to send broadcast');
    } finally {
      setBroadcastLoading(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL hash for navigation
    window.location.hash = value;
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      hotToast.success('Signed out successfully');
    } catch (error) {
      console.error('Error signing out:', error);
      hotToast.error('Failed to sign out');
    }
  };

  // Set initial tab based on URL hash
  useEffect(() => {
    const hash = window.location.hash.replace('#', '');
    if (hash) {
      setActiveTab(hash);
    }
  }, []);

  // Define quickLinks for management tools (matching mobile)
  const quickLinks = [
    {
      title: 'Analytics',
      path: '#analytics',
      icon: <BarChart className="w-6 h-6 text-emerald-400" />,
      desc: 'View stats & trends',
      color: 'from-emerald-600 to-emerald-500',
      category: 'insights'
    },
    {
      title: 'Earnings',
      path: '#earnings',
      icon: <DollarSign className="w-6 h-6 text-green-400" />,
      desc: 'Daily earnings & settlements',
      color: 'from-green-600 to-green-500',
      category: 'insights'
    },
    {
      title: 'Bookings',
      path: '#bookings',
      icon: <Calendar className="w-6 h-6 text-blue-400" />,
      desc: 'Manage all bookings',
      color: 'from-blue-500 to-blue-400',
      category: 'operations'
    },
    {
      title: 'Book for Customer',
      path: '#bookings?tab=admin-booking',
      icon: <UserCircle className="w-6 h-6 text-indigo-400" />,
      desc: 'Create bookings for customers',
      color: 'from-indigo-500 to-indigo-400',
      category: 'operations'
    },
    {
      title: 'Block Slots',
      path: '#bookings?tab=slot-blocking',
      icon: <Ban className="w-6 h-6 text-red-400" />,
      desc: 'Block time slots',
      color: 'from-red-500 to-red-400',
      category: 'operations'
    },
    {
      title: 'Venues',
      path: '#venues',
      icon: <Map className="w-6 h-6 text-purple-400" />,
      desc: 'Edit your venues',
      color: 'from-purple-500 to-purple-400',
      category: 'management'
    },

    {
      title: 'Reviews',
      path: '#reviews',
      icon: <Star className="w-6 h-6 text-yellow-400" />,
      desc: 'Customer feedback',
      color: 'from-yellow-500 to-yellow-400',
      category: 'customer'
    },

    {
      title: 'Help Desk',
      path: '#help',
      icon: <HelpCircle className="w-6 h-6 text-pink-400" />,
      desc: 'Support requests',
      color: 'from-pink-500 to-pink-400',
      category: 'customer'
    },
    {
      title: 'Super Slot Manager',
      path: '/admin/super-slot-management',
      icon: <Ban className="w-6 h-6 text-red-400" />,
      desc: 'Advanced slot blocking',
      color: 'from-red-600 to-red-500',
      category: 'super_admin'
    }
  ];

  return <div className="p-6 bg-gradient-to-br from-slate-900 via-slate-800 to-black min-h-screen text-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className="text-gray-400">Manage venues, courts, bookings and more</p>
          </div>

          {/* Support Button and Sign Out Button - Top Right */}
          <div className="flex items-center gap-4 mt-4 md:mt-0">
            <div className="flex items-center gap-3">
              <UserCircle className="w-8 h-8 text-emerald-400" />
              <div className="hidden md:block">
                <div className="text-sm font-medium text-white">
                  {getDisplayName(user?.user_metadata?.full_name, user?.email) || 'Admin'}
                </div>
                <div className="text-xs text-emerald-300">
                  {userRole === 'super_admin' ? 'Super Admin' : 'Admin'}
                </div>
              </div>
            </div>

            {/* Support Button for Admins */}
            <SupportButton variant="admin" />

            <Button
              onClick={handleSignOut}
              variant="outline"
              size="sm"
              className="flex items-center gap-2 bg-red-600/20 border-red-500/50 text-red-300 hover:bg-red-600/30 hover:text-red-200 transition-all"
            >
              <LogOut className="w-4 h-4" />
              <span className="hidden sm:inline">Sign Out</span>
            </Button>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
          <div className="overflow-x-auto pb-2">
            <TabsList className="bg-gradient-to-r from-slate-800/60 to-slate-700/40 backdrop-blur-sm border border-emerald-500/20 rounded-2xl">
              <TabsTrigger value="dashboard" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Dashboard</TabsTrigger>
              <TabsTrigger value="analytics" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Analytics</TabsTrigger>
              <TabsTrigger value="bookings" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Bookings</TabsTrigger>
              <TabsTrigger value="earnings" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Earnings</TabsTrigger>
              <TabsTrigger value="venues" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Venues</TabsTrigger>
              {/* Hide Reviews, Help Desk for venue admins (role: admin) */}
              {userRole === 'super_admin' && (
                <>
                  <TabsTrigger value="reviews" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Reviews</TabsTrigger>
                  <TabsTrigger value="help" className="rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:shadow-lg transition-all duration-300">Help Desk</TabsTrigger>
                </>
              )}
            </TabsList>
          </div>
          
          <TabsContent value="dashboard" className="space-y-6">
            {/* Modern Welcome Section */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 via-slate-700/30 to-emerald-900/20 backdrop-blur-sm border border-emerald-500/20 shadow-2xl">
              {/* Background decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-emerald-500/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-emerald-400/10 rounded-full blur-lg"></div>

              {/* Content */}
              <div className="relative z-10 p-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/30 flex items-center justify-center backdrop-blur-sm">
                    <span className="text-2xl">👋</span>
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-white to-emerald-100 bg-clip-text text-transparent">
                      Welcome back, {user?.user_metadata?.full_name?.split(' ')[0] || 'Admin'}!
                    </h2>
                    <p className="text-emerald-200/90 text-lg mt-1">
                      Manage your Grid२Play venues and bookings efficiently
                    </p>
                  </div>
                </div>

                {/* Quick stats or additional info */}
                <div className="flex items-center gap-6 mt-6 pt-4 border-t border-emerald-500/20">
                  <div className="flex items-center gap-2 text-emerald-300">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">System Online</span>
                  </div>
                  <div className="text-emerald-200/70 text-sm">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {/* Shine effect */}
              <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-700">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] hover:translate-x-[200%] transition-transform duration-1000"></div>
              </div>
            </div>

            {/* Key Metrics Dashboard */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Activity className="w-6 h-6 mr-2" />
                Today's Performance
              </h3>
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-slate-800/60 to-slate-700/40 backdrop-blur-sm rounded-2xl border border-emerald-500/20">
                  <TabsTrigger value="overview" className="text-sm py-3 rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300">Overview</TabsTrigger>
                  <TabsTrigger value="bookings" className="text-sm py-3 rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300">Bookings</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="mt-4">
                  <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20 shadow-xl">
                    {stats.isLoading ? (
                      <div className="flex justify-center items-center py-8">
                        <Loader2 className="h-10 w-10 animate-spin text-emerald-500" />
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <button onClick={() => setActiveTab('bookings')} className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-6 border border-emerald-200/50 hover:shadow-md transition-all">
                          <div className="text-3xl font-bold text-emerald-700">{stats.todayBookings}</div>
                          <div className="text-sm text-emerald-600 font-medium">Today's Bookings</div>
                        </button>
                        <button onClick={() => setActiveTab('reviews')} className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-6 border border-amber-200/50 hover:shadow-md transition-all">
                          <div className="text-3xl font-bold text-amber-700">{stats.averageRating}</div>
                          <div className="text-sm text-amber-600 font-medium">Avg Rating</div>
                        </button>
                        <button onClick={() => setActiveTab('analytics')} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200/50 hover:shadow-md transition-all">
                          <div className="text-3xl font-bold text-blue-700">{stats.occupancyRate}%</div>
                          <div className="text-sm text-blue-600 font-medium">Occupancy</div>
                        </button>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="bookings" className="mt-4">
                  <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20 shadow-xl">
                    {stats.isLoading ? (
                      <div className="flex justify-center items-center py-8">
                        <Loader2 className="h-10 w-10 animate-spin text-emerald-500" />
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <button onClick={() => setActiveTab('bookings')} className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6 border border-orange-200/50 hover:shadow-md transition-all">
                          <Clock className="h-6 w-6 mb-3 text-orange-600" />
                          <div className="text-2xl font-bold text-orange-700">{stats.pendingBookings}</div>
                          <div className="text-sm text-orange-600 font-medium">Pending Approval</div>
                        </button>
                        <button onClick={() => setActiveTab('bookings')} className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-6 border border-emerald-200/50 hover:shadow-md transition-all">
                          <Calendar className="h-6 w-6 mb-3 text-emerald-600" />
                          <div className="text-2xl font-bold text-emerald-700">{stats.upcomingBookings}</div>
                          <div className="text-sm text-emerald-600 font-medium">Upcoming (7 days)</div>
                        </button>
                      </div>
                    )}
                  </div>
                </TabsContent>


              </Tabs>
            </div>

            {/* Management Tools - Categorized */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                <TrendingUp className="w-6 h-6 mr-2" />
                Management Tools
              </h3>

              {/* Business Insights */}
              <div className="mb-8">
                <h4 className="text-lg font-medium text-emerald-300 mb-4 uppercase tracking-wider">📊 Business Insights</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {quickLinks.filter(link => link.category === 'insights').map(link => (
                    link.path.startsWith('#') ? (
                      <button
                        onClick={() => setActiveTab(link.path.replace('#', ''))}
                        key={link.title}
                        className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-6 flex flex-col items-start transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                      >
                        <div className="mb-3 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                        <div className="text-white font-semibold text-lg mb-2">{link.title}</div>
                        <div className="text-sm text-white/90 leading-tight">{link.desc}</div>
                        <ChevronRight className="absolute bottom-4 right-4 w-5 h-5 text-white/70" />
                      </button>
                    ) : (
                      <Link
                        to={link.path}
                        key={link.title}
                        className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-6 flex flex-col items-start transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                      >
                        <div className="mb-3 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                        <div className="text-white font-semibold text-lg mb-2">{link.title}</div>
                        <div className="text-sm text-white/90 leading-tight">{link.desc}</div>
                        <ChevronRight className="absolute bottom-4 right-4 w-5 h-5 text-white/70" />
                      </Link>
                    )
                  ))}
                </div>
              </div>

              {/* Daily Operations */}
              <div className="mb-8">
                <h4 className="text-lg font-medium text-emerald-300 mb-4 uppercase tracking-wider">⚡ Daily Operations</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {quickLinks.filter(link => link.category === 'operations').map(link => (
                    <button
                      onClick={() => {
                        const [tab, query] = link.path.replace('#', '').split('?');
                        setActiveTab(tab);
                        if (query) {
                          // Store the tab parameter for BookingManagement to use
                          sessionStorage.setItem('bookingTab', new URLSearchParams(query).get('tab') || 'bookings');
                        }
                      }}
                      key={link.title}
                      className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-6 flex flex-col items-start transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                    >
                      <div className="mb-3 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                      <div className="text-white font-semibold text-lg mb-2">{link.title}</div>
                      <div className="text-sm text-white/90 leading-tight">{link.desc}</div>
                      <ChevronRight className="absolute bottom-4 right-4 w-5 h-5 text-white/70" />
                    </button>
                  ))}
                </div>
              </div>

              {/* Venue Management */}
              <div className="mb-8">
                <h4 className="text-lg font-medium text-emerald-300 mb-4 uppercase tracking-wider">🏟️ Venue Management</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {quickLinks.filter(link => link.category === 'management').map(link => (
                    <button
                      onClick={() => setActiveTab(link.path.replace('#', ''))}
                      key={link.title}
                      className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-6 flex flex-col items-start transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                    >
                      <div className="mb-3 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                      <div className="text-white font-semibold text-lg mb-2">{link.title}</div>
                      <div className="text-sm text-white/90 leading-tight">{link.desc}</div>
                      <ChevronRight className="absolute bottom-4 right-4 w-5 h-5 text-white/70" />
                    </button>
                  ))}
                </div>
              </div>

              {/* Super Admin Tools - Only visible to super_admin */}
              {userRole === 'super_admin' && (
                <div className="mb-8">
                  <h4 className="text-lg font-medium text-red-300 mb-4 uppercase tracking-wider">🔧 Super Admin Tools</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {quickLinks.filter(link => link.category === 'super_admin').map(link => (
                      <Link
                        to={link.path}
                        key={link.title}
                        className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-6 flex flex-col items-start transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                      >
                        <div className="mb-3 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                        <div className="text-white font-semibold text-lg mb-2">{link.title}</div>
                        <div className="text-sm text-white/90 leading-tight">{link.desc}</div>
                        <ChevronRight className="absolute bottom-4 right-4 w-5 h-5 text-white/70" />
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* Customer Relations - Only for super admins */}
              {userRole === 'super_admin' && (
                <div className="mb-8">
                  <h4 className="text-lg font-medium text-emerald-300 mb-4 uppercase tracking-wider">🤝 Customer Relations</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {quickLinks.filter(link => link.category === 'customer').map(link => (
                      <button
                        onClick={() => setActiveTab(link.path.replace('#', ''))}
                        key={link.title}
                        className={`rounded-xl shadow-sm bg-gradient-to-r ${link.color} p-6 flex items-center transition-all hover:shadow-lg hover:scale-105 relative overflow-hidden group border border-white/20`}
                      >
                        <div className="mr-4 p-3 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                        <div className="flex-1">
                          <div className="text-white font-semibold text-lg">{link.title}</div>
                          <div className="text-sm text-white/90">{link.desc}</div>
                        </div>
                        <ChevronRight className="w-6 h-6 text-white/70" />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Revenue Analytics */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                <Banknote className="w-6 h-6 mr-2" />
                Revenue Analytics
              </h3>

              {/* Custom Date Range Revenue */}
              <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20 shadow-xl mb-6">
                <h4 className="text-lg font-medium text-emerald-300 mb-4">Custom Date Range Analysis</h4>
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <input
                      type="date"
                      value={customStartDate ? format(customStartDate, 'yyyy-MM-dd') : ''}
                      onChange={e => setCustomStartDate(e.target.value ? new Date(e.target.value) : null)}
                      className="bg-emerald-50 text-emerald-800 rounded-lg px-4 py-3 text-sm border border-emerald-200 flex-1"
                    />
                    <span className="text-emerald-600 self-center text-sm font-medium">to</span>
                    <input
                      type="date"
                      value={customEndDate ? format(customEndDate, 'yyyy-MM-dd') : ''}
                      onChange={e => setCustomEndDate(e.target.value ? new Date(e.target.value) : null)}
                      className="bg-emerald-50 text-emerald-800 rounded-lg px-4 py-3 text-sm border border-emerald-200 flex-1"
                    />
                  </div>

                  {/* NEW: Venue Selector for Custom Revenue Report with Confirmation */}
                  <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium text-emerald-300">Filter by Venue</label>
                    <div className="flex gap-2 items-end">
                      <select
                        value={pendingVenueSelection}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setPendingVenueSelection(newValue);
                          if (selectedRevenueVenueId === 'all' && newValue !== 'all') {
                            setShowVenueConfirmation(true);
                          } else if (newValue === 'all' || newValue === selectedRevenueVenueId) {
                            setSelectedRevenueVenueId(newValue);
                            setShowVenueConfirmation(false);
                          } else {
                            setShowVenueConfirmation(true);
                          }
                        }}
                        className="bg-black text-white rounded-lg px-4 py-3 text-sm border border-gray-600 flex-1"
                      >
                        {userRole === 'super_admin' && (
                          <option value="all" className="bg-black text-white">All Venues</option>
                        )}
                        {venuesWithStats
                          .filter(venue =>
                            userRole === 'super_admin' ||
                            adminVenues.some(v => v.venue_id === venue.id)
                          )
                          .map(venue => (
                            <option key={venue.id} value={venue.id} className="bg-black text-white">
                              {venue.name}
                            </option>
                          ))
                        }
                      </select>

                      {/* Confirmation Button */}
                      {showVenueConfirmation && (
                        <button
                          onClick={() => {
                            setSelectedRevenueVenueId(pendingVenueSelection);
                            setShowVenueConfirmation(false);
                          }}
                          className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors shadow-sm"
                        >
                          ✓ Confirm
                        </button>
                      )}
                    </div>

                    {/* Confirmation Message */}
                    {showVenueConfirmation && (
                      <div className="text-xs text-emerald-600 bg-emerald-50 p-2 rounded border border-emerald-200">
                        Click "Confirm" to apply venue filter and update the revenue report
                      </div>
                    )}
                  </div>

                  {/* PHASE 4: HUDLE APPROACH - Separate Online vs Offline Report Sections */}
                  <div className="space-y-4">
                    {/* Online Bookings Section (Settlement-Affecting) */}
                    <div className="bg-gradient-to-r from-emerald-100 to-emerald-50 rounded-lg p-4 border border-emerald-300">
                      <div className="flex flex-col gap-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                            <h5 className="text-lg font-semibold text-emerald-800">Online Bookings (Settlement-Affecting)</h5>
                          </div>
                          <Banknote className="h-5 w-5 text-emerald-600" />
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          <div className="bg-white/70 rounded-lg p-3 border border-emerald-200">
                            <div className="text-xl font-bold text-emerald-800">₹{(Number(onlineRevenue) || 0).toFixed(0)}</div>
                            <div className="text-xs text-emerald-600">Gross Revenue</div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-emerald-200">
                            <div className="text-xl font-bold text-emerald-700">{onlineBookings || 0}</div>
                            <div className="text-xs text-emerald-600">Bookings</div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-orange-200">
                            <div className="text-xl font-bold text-orange-700">₹{(onlineTdsAmount || 0).toFixed(2)}</div>
                            <div className="text-xs text-orange-600">TDS Amount</div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-emerald-200">
                            <div className="text-xl font-bold text-emerald-600">₹{(onlineNetRevenue || 0).toFixed(0)}</div>
                            <div className="text-xs text-emerald-600">Final Settlement</div>
                          </div>
                        </div>

                        <div className="text-xs text-emerald-600 bg-emerald-50 rounded p-2 border border-emerald-200">
                          ✅ <strong>Included in Grid२Play settlements</strong> - These bookings affect your bank account payouts
                        </div>
                      </div>
                    </div>

                    {/* Offline Bookings Section (Informational Only) */}
                    <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg p-4 border border-gray-300">
                      <div className="flex flex-col gap-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                            <h5 className="text-lg font-semibold text-gray-800">Offline Bookings (Informational Only)</h5>
                          </div>
                          <Banknote className="h-5 w-5 text-gray-600" />
                        </div>

                        <div className="grid grid-cols-3 gap-3">
                          <div className="bg-white/70 rounded-lg p-3 border border-gray-200">
                            <div className="text-xl font-bold text-gray-800">₹{(Number(offlineRevenue) || 0).toFixed(0)}</div>
                            <div className="text-xs text-gray-600">Gross Revenue</div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-gray-200">
                            <div className="text-xl font-bold text-gray-700">{offlineBookings || 0}</div>
                            <div className="text-xs text-gray-600">Bookings</div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-gray-200">
                            <div className="text-xl font-bold text-gray-600">₹{(Number(offlineRevenue) || 0).toFixed(0)}</div>
                            <div className="text-xs text-gray-600">Cash Amount</div>
                          </div>
                        </div>

                        <div className="text-xs text-gray-600 bg-gray-50 rounded p-2 border border-gray-200">
                          ℹ️ <strong>NOT part of Grid२Play settlements</strong> - Cash bookings are venue-managed only
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Download Button */}
                  {customStartDate && customEndDate && (
                    <div className="pt-3 border-t border-emerald-200">
                      <Button
                        onClick={downloadRevenueReport}
                        disabled={downloadingReport}
                        size="lg"
                        className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white"
                      >
                        {downloadingReport ? (
                          <>
                            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                            Generating Report...
                          </>
                        ) : (
                          <>
                            <Download className="w-5 h-5 mr-2" />
                            Download Revenue Report
                          </>
                        )}
                      </Button>
                      <p className="text-xs text-emerald-600 mt-2 text-center">
                        Excel sheet with detailed booking analytics & revenue calculations
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Performance Insights */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                <Award className="w-6 h-6 mr-2" />
                Performance Insights
              </h3>

              <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20 shadow-xl">
                <div className="flex justify-between items-center mb-6">
                  <h4 className="text-lg font-medium text-emerald-300">Top Performing Courts</h4>
                  <button onClick={() => setActiveTab('analytics')} className="text-sm text-emerald-400 hover:text-emerald-300">View All</button>
                </div>

                {courtDataLoading || stats.isLoading ? (
                  <div className="flex justify-center items-center py-6">
                    <Loader2 className="h-8 w-8 animate-spin text-emerald-500" />
                  </div>
                ) : popularCourts.length === 0 ? (
                  <div className="text-center py-6 text-emerald-600">
                    <p>No booking data available</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {popularCourts.map((court, index) => (
                      <div key={index} className="flex justify-between items-center bg-emerald-50 p-4 rounded-lg">
                        <span className="text-sm font-medium text-emerald-800">{court.court_name}</span>
                        <span className="text-sm font-bold text-emerald-600">{court.bookings_percentage}% utilized</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>



            {/* Weather Widget */}
            {adminVenues[0] && (
              <div>
                <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                  <Eye className="w-6 h-6 mr-2" />
                  Weather Conditions
                </h3>
                <WeatherWidget venueId={adminVenues[0].venue_id} />
              </div>
            )}

            {/* Venue Subscribers & Broadcasts Section */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white flex items-center">
                  <MessageCircle className="w-6 h-6 mr-2" />
                  Customer Communications
                </h3>

                {/* Super Admin Link */}
                {user?.email && (user.email.includes('admin') || user.email.includes('aniket')) && (
                  <Link
                    to="/admin/super-dashboard"
                    className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors"
                  >
                    <Crown className="w-4 h-4" />
                    Super Admin
                  </Link>
                )}
              </div>
              <div className="space-y-6">
                {adminVenues.map(v => (
                  <div key={v.venue_id} className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20 shadow-xl">
                    <div className="flex flex-col space-y-4">
                      <div>
                        <div className="text-white font-semibold text-xl">{venuesWithStats.find(venue => venue.id === v.venue_id)?.name || 'Venue'}</div>
                        <div className="text-sm text-emerald-300">Subscribers: <span className="text-emerald-400 font-bold">{venueSubscribers[v.venue_id] || 0}</span></div>
                      </div>

                      {/* Broadcast History */}
                      <div>
                        <div className="text-sm text-emerald-300 mb-3 font-medium">Recent Broadcasts</div>
                        <div className="bg-emerald-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                          {(broadcastHistory[v.venue_id] || []).slice(0, 3).map((b) => (
                            <div key={b.id} className="text-xs border-b border-emerald-100 pb-3 mb-3 last:border-b-0 last:mb-0">
                              <div className="flex justify-between items-start">
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium text-emerald-800 truncate">{b.title}</div>
                                  <div className="text-emerald-600 truncate">{b.message}</div>
                                </div>
                                <div className="ml-3 flex-shrink-0">
                                  {b.approved ? (
                                    <span className="text-green-600 text-xs font-medium">✓ Sent</span>
                                  ) : (
                                    <span className="text-yellow-600 text-xs font-medium">⏳ Pending</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                          {(broadcastHistory[v.venue_id] || []).length === 0 && (
                            <div className="text-emerald-500 text-xs">No broadcasts yet</div>
                          )}
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          className="bg-emerald-600 hover:bg-emerald-700 text-white"
                          onClick={() => setBroadcastModal({ open: true, venueId: v.venue_id })}
                        >
                          Send Notification
                        </Button>
                      </div>

                      {/* Notification Analytics */}
                      <div className="mt-6">
                        <NotificationAnalytics
                          venueId={v.venue_id}
                          venueName={venuesWithStats.find(venue => venue.id === v.venue_id)?.name || 'Venue'}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Dialog open={broadcastModal.open} onOpenChange={open => setBroadcastModal({ open, venueId: open ? broadcastModal.venueId : null })}>
                <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-white">Send Broadcast Notification</DialogTitle>
                  </DialogHeader>

                  <div className="space-y-6">
                    {/* Template Library */}
                    <NotificationTemplateLibrary
                      onUseTemplate={(title, message) => {
                        setBroadcastTitle(title);
                        setBroadcastMessage(message);
                      }}
                      venueName={venuesWithStats.find(venue => venue.id === broadcastModal.venueId)?.name || ''}
                    />

                    {/* Custom Message Form */}
                    <div className="space-y-4">
                      <h4 className="text-white font-medium">Custom Message</h4>
                      <input
                        className="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                        placeholder="Title"
                        value={broadcastTitle}
                        onChange={e => setBroadcastTitle(e.target.value)}
                        maxLength={80}
                      />
                      <textarea
                        className="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                        placeholder="Message"
                        value={broadcastMessage}
                        onChange={e => setBroadcastMessage(e.target.value)}
                        rows={4}
                        maxLength={300}
                      />
                    </div>
                  </div>

                  <DialogFooter>
                    <Button
                      onClick={handleSendBroadcast}
                      disabled={broadcastLoading || !broadcastTitle || !broadcastMessage}
                      className="bg-emerald-600 hover:bg-emerald-700 text-white"
                    >
                      {broadcastLoading ? 'Sending...' : 'Send for Approval'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setBroadcastModal({ open: false, venueId: null })}
                      className="border-gray-600 text-gray-300"
                    >
                      Cancel
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <AnalyticsDesktop userRole={userRole} adminVenues={adminVenues} />
          </TabsContent>

          <TabsContent value="bookings">
            <BookingManagement userRole={userRole} adminVenues={adminVenues} user={user} />
          </TabsContent>

          <TabsContent value="earnings">
            <EarningsDashboard userRole={userRole} adminVenues={adminVenues} />
          </TabsContent>

          <TabsContent value="venues">
            <VenueManagement userRole={userRole} adminVenues={adminVenues} />
          </TabsContent>

          {/* Only show Reviews, Help Desk for super admins */}
          {userRole === 'super_admin' && (
            <>
              <TabsContent value="reviews">
                <ReviewManagement userRole={userRole} adminVenues={adminVenues} />
              </TabsContent>

              <TabsContent value="help">
                <HelpRequestsManagement userRole={userRole} />
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>;
};
export default AdminHome;
