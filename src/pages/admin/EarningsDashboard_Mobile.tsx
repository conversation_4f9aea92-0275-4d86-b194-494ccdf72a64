
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link, useNavigate } from 'react-router-dom';
import {
  ArrowLeft, Calendar, DollarSign, TrendingUp,
  Clock, Banknote, Activity, Eye, ChevronRight,
  Loader2, AlertCircle, CheckCircle, RefreshCw, Info,
  ChevronLeft, ChevronDown, ChevronUp, Star, Award, Target
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { format, startOfWeek, endOfWeek, addDays, subDays } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";
import { getActiveDaysDisplay, getActiveDaysLabel } from '@/utils/weeklyUtils';

interface DailyEarnings {
  venue_id: string;
  venue_name: string;
  cycle_date: string;
  cycle_day_name: string;
  total_bookings: number;
  confirmed_bookings: number;

  // Legacy columns (now contain online values only for settlement compatibility)
  gross_revenue: number;
  platform_fee_amount: number;
  net_revenue: number;

  // PHASE 3: New online/offline separation columns
  online_gross_revenue?: number;
  online_platform_fees?: number;
  online_net_revenue?: number;
  online_bookings?: number;
  offline_gross_revenue?: number;
  offline_platform_fees?: number;
  offline_net_revenue?: number;
  offline_bookings?: number;

  is_current_day: boolean;
  is_frozen: boolean;
}

interface WeeklySummary {
  week_start: string;
  week_end: string;

  // PHASE 3: Online/offline separation
  online_gross: number;
  online_net: number;
  online_bookings: number;
  offline_gross: number;
  offline_net: number;
  offline_bookings: number;

  // Legacy totals
  total_gross: number;
  total_net: number;
  total_bookings: number;
  days_with_data: number;
}

interface EarningsDashboardMobileProps {
  userRole?: string;
  adminVenues?: Array<{ venue_id: string; venue_name?: string }>;
}

const EarningsDashboard_Mobile: React.FC<EarningsDashboardMobileProps> = ({
  userRole: propUserRole,
  adminVenues: propAdminVenues
}) => {
  const { user, userRole: contextUserRole } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Use props if provided, otherwise use context
  const userRole = propUserRole || contextUserRole;
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string; venue_name?: string }>>(propAdminVenues || []);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dailyEarnings, setDailyEarnings] = useState<DailyEarnings[]>([]);
  const [weeklySummary, setWeeklySummary] = useState<WeeklySummary | null>(null);
  const [selectedVenue, setSelectedVenue] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));

  // Collapsible sections state
  const [todayPerformanceExpanded, setTodayPerformanceExpanded] = useState(false);
  const [weekSummaryExpanded, setWeekSummaryExpanded] = useState(false);
  const [dailyEarningsExpanded, setDailyEarningsExpanded] = useState(false);

  // Redirect to desktop if not mobile
  useEffect(() => {
    if (!isMobile) {
      navigate('/admin/earnings');
    }
  }, [isMobile, navigate]);

  // FIX: Fetch admin venues for venue filter
  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (!user?.id || propAdminVenues) return; // Skip if props provided

      try {
        if (userRole === 'admin') {
          const { data, error } = await supabase.rpc('get_admin_venues');
          if (!error && data) {
            // Fetch venue names
            const venueIds = data.map((v: { venue_id: string }) => v.venue_id);
            const { data: venueDetails, error: venueError } = await supabase
              .from('venues')
              .select('id, name')
              .in('id', venueIds)
              .eq('is_active', true);

            if (!venueError && venueDetails) {
              const venuesWithNames = data.map((v: { venue_id: string }) => ({
                venue_id: v.venue_id,
                venue_name: venueDetails.find(vd => vd.id === v.venue_id)?.name
              }));
              setAdminVenues(venuesWithNames);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };

    fetchAdminVenues();
  }, [user?.id, userRole, propAdminVenues]);

  // Check if we're viewing current week or historical week
  const isCurrentWeek = () => {
    const today = new Date();
    const currentWeekStartDate = startOfWeek(today, { weekStartsOn: 1 });
    return format(currentWeekStart, 'yyyy-MM-dd') === format(currentWeekStartDate, 'yyyy-MM-dd');
  };

  // Fetch earnings data
  const fetchEarningsData = async (showRefreshIndicator = false) => {
    if (!user?.id) return;

    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // PHASE 3: Use get_admin_daily_earnings function (same as desktop)
      const weekStart = format(currentWeekStart, 'yyyy-MM-dd');
      const weekEnd = format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd');

      const { data: earningsData, error: earningsError } = await supabase
        .rpc('get_admin_daily_earnings', {
          p_start_date: weekStart,
          p_end_date: weekEnd,
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue
        });

      if (earningsError) {
        console.error('Error fetching daily earnings:', earningsError);
        throw earningsError;
      }

      if (!earningsData || earningsData.length === 0) {
        console.log('No earnings data found');
        setDailyEarnings([]);
        setWeeklySummary({
          week_start: weekStart,
          week_end: weekEnd,
          online_gross: 0,
          online_net: 0,
          online_bookings: 0,
          offline_gross: 0,
          offline_net: 0,
          offline_bookings: 0,
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        return;
      }

      // PHASE 3: Process daily earnings data (same as desktop approach)
      console.log('Daily earnings data found:', earningsData);

      // PHASE 3: Use daily_earnings data directly (same as desktop)
      const processedEarnings = earningsData;

      // PHASE 3: Calculate weekly summary with online/offline separation (same as desktop)
      if (processedEarnings && processedEarnings.length > 0) {
        const summary: WeeklySummary = {
          week_start: weekStart,
          week_end: weekEnd,

          // PHASE 3: Online metrics (settlement-affecting)
          online_gross: processedEarnings.reduce((sum, day: any) => sum + ((day.online_gross_revenue || day.gross_revenue) || 0), 0),
          online_net: processedEarnings.reduce((sum, day: any) => sum + ((day.online_net_revenue || day.net_revenue) || 0), 0),
          online_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.online_bookings || 0), 0),

          // PHASE 3: Offline metrics (informational)
          offline_gross: processedEarnings.reduce((sum, day: any) => sum + (day.offline_gross_revenue || 0), 0),
          offline_net: processedEarnings.reduce((sum, day: any) => sum + (day.offline_net_revenue || 0), 0),
          offline_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.offline_bookings || 0), 0),

          // Legacy totals
          total_gross: processedEarnings.reduce((sum, day: any) => sum + (day.gross_revenue || 0), 0),
          total_net: processedEarnings.reduce((sum, day: any) => sum + (day.net_revenue || 0), 0),
          total_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.total_bookings || 0), 0),
          days_with_data: processedEarnings.filter((day: any) => day.total_bookings > 0).length
        };
        setWeeklySummary(summary);

        // PHASE 3: Use processed earnings directly for daily earnings display
        setDailyEarnings(processedEarnings);
      } else {
        setWeeklySummary({
          week_start: format(currentWeekStart, 'yyyy-MM-dd'),
          week_end: format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
          online_gross: 0,
          online_net: 0,
          online_bookings: 0,
          offline_gross: 0,
          offline_net: 0,
          offline_bookings: 0,
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        setDailyEarnings([]);
      }

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchEarningsData();
  }, [user?.id, currentWeekStart, selectedVenue]);

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = direction === 'prev' 
      ? subDays(currentWeekStart, 7)
      : addDays(currentWeekStart, 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Get today's earnings
  const todayEarnings = dailyEarnings.find(day => day.is_current_day);

  if (loading) {
    return (
      <div className="min-h-screen bg-navy-dark text-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-emerald-900/20 to-black relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1485395037613-e83d5c1f5290?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')] opacity-5 bg-center bg-cover"></div>

      {/* Header - Mobile-First Design */}
      <div className="sticky top-0 z-10 bg-gradient-to-r from-emerald-600 to-emerald-800 shadow-xl border-b border-emerald-400/30">
        <div className="flex items-center justify-between px-4 py-4">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/mobile-home')}
              className="mr-3 p-2 rounded-full hover:bg-white/10 transition-all duration-300 min-h-[48px] min-w-[48px] flex items-center justify-center"
            >
              <ArrowLeft className="h-6 w-6 text-white" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white tracking-wide">Earnings</h1>
              <p className="text-emerald-200 text-sm opacity-90">Revenue & Settlements</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fetchEarningsData(true)}
            disabled={refreshing}
            className="text-white hover:bg-white/10 min-h-[48px] min-w-[48px] rounded-full"
          >
            <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Venue Filter - Mobile-Optimized */}
        {adminVenues.length > 0 && (
          <div className="px-4 pb-4">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span className="text-sm text-emerald-200 font-medium">Venue Filter:</span>
                <Select value={selectedVenue} onValueChange={setSelectedVenue}>
                  <SelectTrigger className="flex-1 bg-black/40 border-white/20 text-white text-sm rounded-lg min-h-[44px]">
                    <SelectValue placeholder="Select venue" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-white/20 rounded-xl">
                    <SelectItem value="all" className="text-white hover:bg-emerald-600/20">All Venues</SelectItem>
                    {adminVenues.map((venue) => (
                      <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white hover:bg-emerald-600/20">
                        {venue.venue_name || `Venue ${venue.venue_id.slice(0, 8)}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content - Mobile-First Container */}
      <div className="container mx-auto px-4 py-6 space-y-6 relative z-10">
        {/* Time Period Context Banner - Mobile-Optimized */}
        <div className={`bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border backdrop-blur-sm transition-all duration-300 ${
          isCurrentWeek()
            ? 'border-emerald-900/20 hover:border-emerald-700/50'
            : 'border-blue-900/20 hover:border-blue-700/50'
        }`}>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 ${
                isCurrentWeek()
                  ? 'bg-emerald-500/20 border border-emerald-500/30'
                  : 'bg-blue-500/20 border border-blue-500/30'
              }`}>
                {isCurrentWeek() ? (
                  <Activity className="w-6 h-6 text-emerald-400" />
                ) : (
                  <Clock className="w-6 h-6 text-blue-400" />
                )}
              </div>
              <div className="flex-1">
                <h3 className={`text-lg font-bold mb-1 ${
                  isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
                }`}>
                  {isCurrentWeek() ? 'Current Week Earnings' : 'Historical Week Data'}
                </h3>
                <p className="text-sm text-gray-300 leading-relaxed">
                  {isCurrentWeek()
                    ? 'Live earnings data - updates in real-time as bookings are made'
                    : 'Historical data from completed week - final amounts may differ from settlements'
                  }
                </p>
              </div>
              {!isCurrentWeek() && (
                <div className="bg-blue-500/20 text-blue-300 border border-blue-500/30 px-3 py-1 rounded-full text-xs font-medium">
                  <Info className="w-3 h-3 mr-1 inline" />
                  Historical
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Today's Performance - Mobile-First Design */}
        {isCurrentWeek() && (
          <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-emerald-900/20 hover:border-emerald-700/50 backdrop-blur-sm transition-all duration-300">
            <div className="p-6">
              <div
                className="flex items-center justify-between mb-6 cursor-pointer"
                onClick={() => setTodayPerformanceExpanded(!todayPerformanceExpanded)}
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-emerald-500/20 rounded-xl flex items-center justify-center mr-4 border border-emerald-500/30">
                    <Star className="w-6 h-6 text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-emerald-300 mb-1">Today's Performance</h3>
                    <p className="text-sm text-emerald-200 opacity-90">Real-time earnings snapshot</p>
                  </div>
                </div>
                <div className="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center border border-emerald-500/30">
                  {todayPerformanceExpanded ? (
                    <ChevronUp className="w-4 h-4 text-emerald-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-emerald-400" />
                  )}
                </div>
              </div>

              {todayPerformanceExpanded && (todayEarnings ? (
                <div className="space-y-4">
                  {/* Online Revenue Card */}
                  <div className="bg-gradient-to-r from-emerald-500/10 to-emerald-600/10 rounded-xl p-4 border border-emerald-500/20">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                        <span className="text-emerald-300 text-sm font-semibold">Online Revenue</span>
                      </div>
                      <div className="bg-emerald-600/30 px-2 py-1 rounded-full text-xs text-emerald-200 font-medium">
                        Settlement
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center bg-white/5 rounded-lg p-3">
                        <div className="text-2xl font-bold text-emerald-300 mb-1">
                          ₹{((todayEarnings as any).online_gross_revenue || todayEarnings.gross_revenue || 0).toFixed(0)}
                        </div>
                        <div className="text-xs text-emerald-200">Online Gross</div>
                      </div>
                      <div className="text-center bg-white/5 rounded-lg p-3">
                        <div className="text-2xl font-bold text-emerald-300 mb-1">
                          ₹{((todayEarnings as any).online_net_revenue || todayEarnings.net_revenue || 0).toFixed(0)}
                        </div>
                        <div className="text-xs text-emerald-200">Net Settlement</div>
                      </div>
                    </div>
                    <div className="text-center mt-3 pt-3 border-t border-emerald-500/20">
                      <div className="text-sm text-emerald-300 font-medium">
                        {(todayEarnings as any).online_bookings || 0} online bookings
                      </div>
                    </div>
                  </div>

                  {/* Cash Revenue Card */}
                  <div className="bg-gradient-to-r from-gray-500/10 to-gray-600/10 rounded-xl p-4 border border-gray-500/20">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                        <span className="text-gray-300 text-sm font-semibold">Cash Revenue</span>
                      </div>
                      <div className="bg-gray-600/30 px-2 py-1 rounded-full text-xs text-gray-300 font-medium">
                        Info Only
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center bg-white/5 rounded-lg p-3">
                        <div className="text-2xl font-bold text-gray-300 mb-1">
                          ₹{((todayEarnings as any).offline_gross_revenue || 0).toFixed(0)}
                        </div>
                        <div className="text-xs text-gray-200">Cash Gross</div>
                      </div>
                      <div className="text-center bg-white/5 rounded-lg p-3">
                        <div className="text-2xl font-bold text-gray-300 mb-1">
                          ₹{((todayEarnings as any).offline_gross_revenue || 0).toFixed(0)}
                        </div>
                        <div className="text-xs text-gray-200">Venue Keeps</div>
                      </div>
                    </div>
                    <div className="text-center mt-3 pt-3 border-t border-gray-500/20">
                      <div className="text-sm text-gray-300 font-medium">
                        {(todayEarnings as any).offline_bookings || 0} cash bookings
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-emerald-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-emerald-400" />
                  </div>
                  <div className="text-lg font-semibold text-emerald-200 mb-2">No bookings today yet</div>
                  <div className="text-sm text-emerald-300 opacity-80">Revenue will appear when bookings are made</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Week Navigation - Mobile-Optimized */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-gray-900/20 backdrop-blur-sm">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateWeek('prev')}
                className="text-white hover:bg-white/10 min-h-[48px] px-4 rounded-xl border border-white/10"
              >
                <ChevronLeft className="h-5 w-5 mr-2" />
                Previous
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateWeek('next')}
                className="text-white hover:bg-white/10 min-h-[48px] px-4 rounded-xl border border-white/10"
              >
                Next
                <ChevronRight className="h-5 w-5 ml-2" />
              </Button>
            </div>

            <div className="text-center bg-white/5 rounded-xl p-4">
              <div className="font-bold text-white text-lg mb-1">
                {format(currentWeekStart, 'MMM dd')} - {format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'MMM dd, yyyy')}
              </div>
              <div className={`text-sm font-medium ${
                isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
              }`}>
                {isCurrentWeek() ? 'Current Weekly Cycle' : 'Historical Weekly Cycle'}
              </div>
            </div>
          </div>
        </div>

        {/* Weekly Summary - Mobile-First Design */}
        {weeklySummary && (
          <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-gray-900/20 backdrop-blur-sm transition-all duration-300">
            <div className="p-6">
              <div
                className="flex items-center justify-between mb-6 cursor-pointer"
                onClick={() => setWeekSummaryExpanded(!weekSummaryExpanded)}
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4 border border-purple-500/30">
                    <TrendingUp className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-1">Week Summary</h3>
                    <p className="text-sm text-gray-300 opacity-90">Revenue breakdown</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {!isCurrentWeek() && (
                    <div className="bg-blue-500/20 text-blue-300 border border-blue-500/30 px-3 py-1 rounded-full text-xs font-medium">
                      Historical Data
                    </div>
                  )}
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center border border-purple-500/30">
                    {weekSummaryExpanded ? (
                      <ChevronUp className="w-4 h-4 text-purple-400" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-purple-400" />
                    )}
                  </div>
                </div>
              </div>

              {weekSummaryExpanded && (
                <>
                  {/* Mobile Tabbed View - Enhanced */}
                  <Tabs defaultValue="online" className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-black/40 backdrop-blur-sm rounded-xl border border-white/10 mb-6 p-1">
                  <TabsTrigger
                    value="online"
                    className="text-sm py-3 rounded-lg data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300 font-medium"
                  >
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                      Online
                      <span className="ml-2 text-xs bg-emerald-600/30 px-2 py-0.5 rounded-full">Settlement</span>
                    </div>
                  </TabsTrigger>
                  <TabsTrigger
                    value="offline"
                    className="text-sm py-3 rounded-lg data-[state=active]:bg-gray-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-gray-300 transition-all duration-300 font-medium"
                  >
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                      Cash
                      <span className="ml-2 text-xs bg-gray-600/30 px-2 py-0.5 rounded-full">Info</span>
                    </div>
                  </TabsTrigger>
                </TabsList>

                {/* Online Revenue Tab - Mobile-Optimized */}
                <TabsContent value="online" className="mt-0">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gradient-to-br from-emerald-500/20 to-emerald-600/20 rounded-xl p-4 text-center border border-emerald-500/30">
                        <div className="text-2xl font-bold text-emerald-300 mb-1">₹{weeklySummary.online_gross.toFixed(0)}</div>
                        <div className="text-sm text-emerald-200 font-medium mb-1">Online Gross</div>
                        <div className="text-xs text-emerald-300 opacity-80">Goes to settlement</div>
                      </div>
                      <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl p-4 text-center border border-green-500/30">
                        <div className="text-2xl font-bold text-green-300 mb-1">₹{weeklySummary.online_net.toFixed(0)}</div>
                        <div className="text-sm text-green-200 font-medium mb-1">Net Settlement</div>
                        <div className="text-xs text-green-300 opacity-80">Your payout</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl p-4 text-center border border-blue-500/30">
                        <div className="text-2xl font-bold text-blue-300 mb-1">{weeklySummary.online_bookings}</div>
                        <div className="text-sm text-blue-200 font-medium mb-1">Online Bookings</div>
                        <div className="text-xs text-blue-300 opacity-80">Settlement-affecting</div>
                      </div>
                      <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl p-4 text-center border border-purple-500/30">
                        <div className="text-2xl font-bold text-purple-300 mb-1">{getActiveDaysDisplay(currentWeekStart, weeklySummary.days_with_data)}</div>
                        <div className="text-sm text-purple-200 font-medium mb-1">{getActiveDaysLabel(currentWeekStart)}</div>
                        <div className="text-xs text-purple-300 opacity-80">Active period</div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Offline Revenue Tab - Mobile-Optimized */}
                <TabsContent value="offline" className="mt-0">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gradient-to-br from-gray-500/20 to-gray-600/20 rounded-xl p-4 text-center border border-gray-500/30">
                        <div className="text-2xl font-bold text-gray-300 mb-1">₹{weeklySummary.offline_gross.toFixed(0)}</div>
                        <div className="text-sm text-gray-200 font-medium mb-1">Cash Gross</div>
                        <div className="text-xs text-gray-300 opacity-80">Not in settlement</div>
                      </div>
                      <div className="bg-gradient-to-br from-gray-600/20 to-gray-700/20 rounded-xl p-4 text-center border border-gray-600/30">
                        <div className="text-2xl font-bold text-gray-300 mb-1">₹{weeklySummary.offline_gross.toFixed(0)}</div>
                        <div className="text-sm text-gray-200 font-medium mb-1">Venue Keeps</div>
                        <div className="text-xs text-gray-300 opacity-80">100% yours</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gradient-to-br from-gray-400/20 to-gray-500/20 rounded-xl p-4 text-center border border-gray-400/30">
                        <div className="text-2xl font-bold text-gray-300 mb-1">{weeklySummary.offline_bookings}</div>
                        <div className="text-sm text-gray-200 font-medium mb-1">Cash Bookings</div>
                        <div className="text-xs text-gray-300 opacity-80">Informational only</div>
                      </div>
                      <div className="bg-gradient-to-br from-gray-500/20 to-gray-600/20 rounded-xl p-4 text-center border border-gray-500/30">
                        <div className="text-2xl font-bold text-gray-300 mb-1">0</div>
                        <div className="text-sm text-gray-200 font-medium mb-1">Platform Fees</div>
                        <div className="text-xs text-gray-300 opacity-80">No fees on cash</div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                  </Tabs>
                </>
              )}
            </div>
          </div>
        )}

        {/* Daily Earnings Section - Mobile-First Design */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-gray-900/20 backdrop-blur-sm">
          <div className="p-6">
            <div
              className="flex items-center justify-between mb-6 cursor-pointer"
              onClick={() => setDailyEarningsExpanded(!dailyEarningsExpanded)}
            >
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4 border border-blue-500/30">
                  <Calendar className="w-6 h-6 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white mb-1">Daily Earnings</h3>
                  <p className="text-sm text-gray-300 opacity-90">Day-by-day breakdown</p>
                </div>
              </div>
              <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center border border-blue-500/30">
                {dailyEarningsExpanded ? (
                  <ChevronUp className="w-4 h-4 text-blue-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-blue-400" />
                )}
              </div>
            </div>

            {dailyEarningsExpanded && (
              <div className="space-y-4">
              {dailyEarnings.map((day, index) => (
                <div
                  key={`${day.venue_id}-${day.cycle_date}`}
                  className={`rounded-xl p-5 border transition-all duration-300 ${
                    day.is_current_day
                      ? 'bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 border-emerald-500/50 shadow-lg'
                      : day.total_bookings > 0
                        ? 'bg-white/5 border-white/20 hover:bg-white/10'
                        : 'bg-gray-500/10 border-gray-500/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="text-white font-bold text-lg">
                        {day.cycle_day_name}
                      </div>
                      <div className="text-sm text-gray-300 ml-3 bg-white/10 px-2 py-1 rounded-full">
                        {format(new Date(day.cycle_date), 'MMM dd')}
                      </div>
                      {day.is_current_day && (
                        <div className="ml-3 px-3 py-1 bg-emerald-500 text-white text-xs rounded-full font-medium">
                          Today
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-white font-bold text-lg">
                        ₹{(day.online_gross_revenue || day.gross_revenue || 0).toFixed(0)}
                        {(day.offline_gross_revenue || 0) > 0 && (
                          <span className="text-gray-400 text-sm ml-1">
                            +₹{(day.offline_gross_revenue || 0).toFixed(0)}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-300 font-medium">
                        {day.total_bookings} booking{day.total_bookings !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>

                  {day.total_bookings > 0 ? (
                    <div className="space-y-3">
                      {/* Online Revenue Card */}
                      <div className="bg-gradient-to-r from-emerald-500/10 to-emerald-600/10 rounded-lg p-3 border border-emerald-500/20">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                            <span className="text-emerald-300 text-sm font-semibold">Online</span>
                            <span className="ml-2 text-xs bg-emerald-600/30 px-2 py-1 rounded-full text-emerald-200 font-medium">Settlement</span>
                          </div>
                          <div className="text-right">
                            <div className="text-emerald-300 font-bold text-lg">₹{(day.online_gross_revenue || day.gross_revenue || 0).toFixed(0)}</div>
                            <div className="text-emerald-200 text-xs">Net: ₹{(day.online_net_revenue || day.net_revenue || 0).toFixed(0)}</div>
                          </div>
                        </div>
                      </div>

                      {/* Cash Revenue Card */}
                      <div className="bg-gradient-to-r from-gray-500/10 to-gray-600/10 rounded-lg p-3 border border-gray-500/20">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                            <span className="text-gray-300 text-sm font-semibold">Cash</span>
                            <span className="ml-2 text-xs bg-gray-600/30 px-2 py-1 rounded-full text-gray-300 font-medium">Info</span>
                          </div>
                          <div className="text-right">
                            <div className="text-gray-300 font-bold text-lg">₹{(day.offline_gross_revenue || 0).toFixed(0)}</div>
                            <div className="text-gray-200 text-xs">Venue keeps 100%</div>
                          </div>
                        </div>
                      </div>

                      {/* Booking Summary */}
                      <div className="text-center bg-white/5 rounded-lg p-2">
                        <div className="text-sm text-gray-300 font-medium">
                          {(day.online_bookings || 0)} online + {(day.offline_bookings || 0)} cash bookings
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Calendar className="w-6 h-6 text-gray-400" />
                      </div>
                      <div className="text-gray-400 text-sm font-medium">No bookings</div>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-white/10">
                    <div className="text-xs text-gray-400 font-medium">
                      📍 {day.venue_name}
                    </div>
                  </div>
                </div>
              ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions - Mobile-First Design */}
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center mr-4 border border-orange-500/30">
              <Award className="w-6 h-6 text-orange-400" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white mb-1">Quick Actions</h3>
              <p className="text-sm text-gray-300 opacity-90">Access related features</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Link
              to="/admin/settlements-mobile"
              className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-blue-900/20 hover:border-blue-700/50 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl hover:bg-gray-900/70 min-h-[120px]"
            >
              <div className="p-6 flex flex-col items-center text-center h-full justify-center">
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mb-3 border border-blue-500/30">
                  <Banknote className="w-6 h-6 text-blue-400" />
                </div>
                <div className="font-bold text-white mb-1">Settlements</div>
                <div className="text-sm text-blue-200 opacity-90">Weekly payouts</div>
              </div>
            </Link>

            <Link
              to="/admin/analytics-mobile"
              className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-purple-900/20 hover:border-purple-700/50 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl hover:bg-gray-900/70 min-h-[120px]"
            >
              <div className="p-6 flex flex-col items-center text-center h-full justify-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mb-3 border border-purple-500/30">
                  <Eye className="w-6 h-6 text-purple-400" />
                </div>
                <div className="font-bold text-white mb-1">Analytics</div>
                <div className="text-sm text-purple-200 opacity-90">Detailed insights</div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EarningsDashboard_Mobile;
